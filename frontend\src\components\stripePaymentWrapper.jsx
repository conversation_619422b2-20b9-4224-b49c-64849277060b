import React, { useState, useEffect, useRef } from 'react';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import PaymentForm from './paymentForm';
import httpService from '../services/httpService';
import Message from './message';

// Cập nhật publishable key - phải khớp với secret key trong backend
const stripePromise = loadStripe(
  "pk_test_51ReEgVQ2LebguEiky8SYJgQlfD0xKqGZ3EV6RbwHcauQlFDsvd6zo7sj5JiyIUpSz7zY62nrfLkRju8dxoOlEs0P00MfHxwsiJ"
);

export default function StripePaymentWrapper({ id }) {
  const [clientSecret, setClientSecret] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const requestSentRef = useRef(false);

  useEffect(() => {
    const createPaymentIntent = async () => {
      // Nếu đã gửi request hoặc đã có clientSecret, không gửi lại
      if (requestSentRef.current || clientSecret) {
        return;
      }
      
      // Đánh dấu đã gửi request
      requestSentRef.current = true;
      
      try {
        setLoading(true);
        console.log("Creating payment intent for order:", id);
        
        const { data } = await httpService.post("/api/stripe-payment/", {order: id});
        
        if (data.clientSecret) {
          console.log("Received client secret successfully");
          setClientSecret(data.clientSecret);
        } else {
          console.error("No client secret in response:", data);
          setError("Failed to initialize payment form");
        }
      } catch (ex) {
        console.error("Error creating payment intent:", ex);
        console.error("Response data:", ex.response?.data);
        console.error("Response status:", ex.response?.status);

        let errorMessage = "Could not initialize payment: ";
        if (ex.response?.status === 404) {
          errorMessage += "Backend server not running or API endpoint not found";
        } else if (ex.response?.status === 500) {
          errorMessage += "Server error - check Stripe configuration";
        } else if (ex.response?.data?.error) {
          errorMessage += ex.response.data.error;
        } else if (ex.message) {
          errorMessage += ex.message;
        } else {
          errorMessage += "Unknown error occurred";
        }

        setError(errorMessage);
        // Reset flag nếu có lỗi để có thể thử lại
        requestSentRef.current = false;
      } finally {
        setLoading(false);
      }
    };
    
    if (id) {
      createPaymentIntent();
    }
  }, [id, clientSecret]);

  const appearance = {
    theme: "stripe",
    variables: {
      colorPrimary: '#0570de',
    }
  };

  const options = clientSecret ? {
    clientSecret,
    appearance,
  } : {};

  const retryPayment = () => {
    setError('');
    setClientSecret('');
    requestSentRef.current = false;
  };

  if (loading) return <div>Loading payment form...</div>;
  if (error) return (
    <div>
      <Message variant="danger">{error}</Message>
      <button onClick={retryPayment} className="btn btn-primary mt-2">
        Try Again
      </button>
    </div>
  );
  if (!clientSecret) return <Message variant="danger">Could not initialize payment form</Message>;

  return (
    <div className="payment">
      <Elements options={options} stripe={stripePromise}>
        <PaymentForm id={id} />
      </Elements>
    </div>
  );
}
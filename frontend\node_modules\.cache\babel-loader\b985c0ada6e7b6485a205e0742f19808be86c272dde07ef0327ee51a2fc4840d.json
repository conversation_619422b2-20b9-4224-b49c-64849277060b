{"ast": null, "code": "var _jsxFileName = \"D:\\\\CHUONGTRINHHOC\\\\NAM3\\\\DOANTHUCTAP\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\paymentForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { PaymentElement, LinkAuthenticationElement, useStripe, useElements } from \"@stripe/react-stripe-js\";\nimport httpService from \"../services/httpService\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function PaymentForm(_ref) {\n  _s();\n  let {\n    id\n  } = _ref;\n  const stripe = useStripe();\n  const elements = useElements();\n  const [email, setEmail] = useState(\"\");\n  const [message, setMessage] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isReady, setIsReady] = useState(false);\n  let redirectURL = `${window.location.href.split('#')[0] + '#'}/confirmation?id=${id}&success=true`;\n  console.log(redirectURL);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!stripe || !elements) {\n      return;\n    }\n    setIsLoading(true);\n    const {\n      error\n    } = await stripe.confirmPayment({\n      elements,\n      confirmParams: {\n        return_url: redirectURL\n      }\n    });\n    if (error) {\n      if (error.type === \"card_error\" || error.type === \"validation_error\") {\n        setMessage(error.message);\n      } else {\n        setMessage(\"An unexpected error occurred.\");\n      }\n    } else {\n      setMessage(\"Payment succeeded!\");\n    }\n    setIsLoading(false);\n  };\n  const paymentElementOptions = {\n    layout: \"tabs\"\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    id: \"payment-form\",\n    onSubmit: handleSubmit,\n    children: [/*#__PURE__*/_jsxDEV(LinkAuthenticationElement, {\n      id: \"link-authentication-element\",\n      onChange: e => setEmail(e.target.value)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PaymentElement, {\n      id: \"payment-element\",\n      options: paymentElementOptions\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      disabled: isLoading || !stripe || !elements,\n      id: \"submit\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        id: \"button-text\",\n        children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner\",\n          id: \"spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 24\n        }, this) : \"Pay now\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      id: \"payment-message\",\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 19\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n}\n_s(PaymentForm, \"kKbteTd58IqUC3k6/GA1YshCuzg=\", false, function () {\n  return [useStripe, useElements];\n});\n_c = PaymentForm;\nvar _c;\n$RefreshReg$(_c, \"PaymentForm\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "PaymentElement", "LinkAuthenticationElement", "useStripe", "useElements", "httpService", "jsxDEV", "_jsxDEV", "PaymentForm", "_ref", "_s", "id", "stripe", "elements", "email", "setEmail", "message", "setMessage", "isLoading", "setIsLoading", "isReady", "setIsReady", "redirectURL", "window", "location", "href", "split", "console", "log", "handleSubmit", "e", "preventDefault", "error", "confirmPayment", "confirmParams", "return_url", "type", "paymentElementOptions", "layout", "onSubmit", "children", "onChange", "target", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "options", "disabled", "className", "_c", "$RefreshReg$"], "sources": ["D:/CHUONGTRINHHOC/NAM3/DOANTHUCTAP/Python-KienTap-/frontend/src/components/paymentForm.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport {\r\n  PaymentElement,\r\n  LinkAuthenticationElement,\r\n  useStripe,\r\n  useElements,\r\n} from \"@stripe/react-stripe-js\";\r\nimport httpService from \"../services/httpService\";\r\n\r\nexport default function PaymentForm({ id }) {\r\n  const stripe = useStripe();\r\n  const elements = useElements();\r\n\r\n  const [email, setEmail] = useState(\"\");\r\n  const [message, setMessage] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isReady, setIsReady] = useState(false);\r\n\r\n  let redirectURL = `${window.location.href.split('#')[0] + '#'}/confirmation?id=${id}&success=true`\r\n  console.log(redirectURL)\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n  \r\n    if (!stripe || !elements) {\r\n      return;\r\n    }\r\n  \r\n    setIsLoading(true);\r\n\r\n    const { error } = await stripe.confirmPayment({\r\n      elements,\r\n      confirmParams: {\r\n        return_url: redirectURL\r\n      },\r\n    });\r\n  \r\n    if (error) {\r\n      if (error.type === \"card_error\" || error.type === \"validation_error\") {\r\n        setMessage(error.message);\r\n      } else {\r\n        setMessage(\"An unexpected error occurred.\");\r\n      }\r\n    } else {\r\n      setMessage(\"Payment succeeded!\");\r\n    }\r\n  \r\n    setIsLoading(false);\r\n  };\r\n\r\n  const paymentElementOptions = {\r\n    layout: \"tabs\",\r\n  };\r\n\r\n  return (\r\n    <form id=\"payment-form\" onSubmit={handleSubmit}>\r\n      <LinkAuthenticationElement\r\n        id=\"link-authentication-element\"\r\n        onChange={(e) => setEmail(e.target.value)}\r\n      />\r\n      <PaymentElement id=\"payment-element\" options={paymentElementOptions} />\r\n      <button disabled={isLoading || !stripe || !elements} id=\"submit\">\r\n        <span id=\"button-text\">\r\n          {isLoading ? <div className=\"spinner\" id=\"spinner\"></div> : \"Pay now\"}\r\n        </span>\r\n      </button>\r\n      {message && <div id=\"payment-message\">{message}</div>}\r\n    </form>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SACEC,cAAc,EACdC,yBAAyB,EACzBC,SAAS,EACTC,WAAW,QACN,yBAAyB;AAChC,OAAOC,WAAW,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,eAAe,SAASC,WAAWA,CAAAC,IAAA,EAAS;EAAAC,EAAA;EAAA,IAAR;IAAEC;EAAG,CAAC,GAAAF,IAAA;EACxC,MAAMG,MAAM,GAAGT,SAAS,EAAE;EAC1B,MAAMU,QAAQ,GAAGT,WAAW,EAAE;EAE9B,MAAM,CAACU,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAE7C,IAAIsB,WAAW,GAAI,GAAEC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAI,oBAAmBf,EAAG,eAAc;EAClGgB,OAAO,CAACC,GAAG,CAACN,WAAW,CAAC;EAExB,MAAMO,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,EAAE;IAElB,IAAI,CAACnB,MAAM,IAAI,CAACC,QAAQ,EAAE;MACxB;IACF;IAEAM,YAAY,CAAC,IAAI,CAAC;IAElB,MAAM;MAAEa;IAAM,CAAC,GAAG,MAAMpB,MAAM,CAACqB,cAAc,CAAC;MAC5CpB,QAAQ;MACRqB,aAAa,EAAE;QACbC,UAAU,EAAEb;MACd;IACF,CAAC,CAAC;IAEF,IAAIU,KAAK,EAAE;MACT,IAAIA,KAAK,CAACI,IAAI,KAAK,YAAY,IAAIJ,KAAK,CAACI,IAAI,KAAK,kBAAkB,EAAE;QACpEnB,UAAU,CAACe,KAAK,CAAChB,OAAO,CAAC;MAC3B,CAAC,MAAM;QACLC,UAAU,CAAC,+BAA+B,CAAC;MAC7C;IACF,CAAC,MAAM;MACLA,UAAU,CAAC,oBAAoB,CAAC;IAClC;IAEAE,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMkB,qBAAqB,GAAG;IAC5BC,MAAM,EAAE;EACV,CAAC;EAED,oBACE/B,OAAA;IAAMI,EAAE,EAAC,cAAc;IAAC4B,QAAQ,EAAEV,YAAa;IAAAW,QAAA,gBAC7CjC,OAAA,CAACL,yBAAyB;MACxBS,EAAE,EAAC,6BAA6B;MAChC8B,QAAQ,EAAGX,CAAC,IAAKf,QAAQ,CAACe,CAAC,CAACY,MAAM,CAACC,KAAK;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAC1C,eACFxC,OAAA,CAACN,cAAc;MAACU,EAAE,EAAC,iBAAiB;MAACqC,OAAO,EAAEX;IAAsB;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG,eACvExC,OAAA;MAAQ0C,QAAQ,EAAE/B,SAAS,IAAI,CAACN,MAAM,IAAI,CAACC,QAAS;MAACF,EAAE,EAAC,QAAQ;MAAA6B,QAAA,eAC9DjC,OAAA;QAAMI,EAAE,EAAC,aAAa;QAAA6B,QAAA,EACnBtB,SAAS,gBAAGX,OAAA;UAAK2C,SAAS,EAAC,SAAS;UAACvC,EAAE,EAAC;QAAS;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAO,GAAG;MAAS;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAChE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACA,EACR/B,OAAO,iBAAIT,OAAA;MAAKI,EAAE,EAAC,iBAAiB;MAAA6B,QAAA,EAAExB;IAAO;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAO;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QAChD;AAEX;AAACrC,EAAA,CA5DuBF,WAAW;EAAA,QAClBL,SAAS,EACPC,WAAW;AAAA;AAAA+C,EAAA,GAFN3C,WAAW;AAAA,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
[{"D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\index.js": "1", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\reportWebVitals.js": "2", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\App.js": "3", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\context\\cartContext.js": "4", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\context\\userContext.js": "5", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\context\\productsContext.js": "6", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx": "7", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx": "8", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx": "9", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx": "10", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx": "11", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx": "12", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx": "13", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx": "14", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx": "15", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx": "16", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\footer.jsx": "17", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx": "18", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx": "19", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx": "20", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx": "21", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\header.jsx": "22", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx": "23", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx": "24", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx": "25", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx": "26", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx": "27", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx": "28", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminReviews.jsx": "29", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\services\\httpService.js": "30", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\loader.jsx": "31", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\message.jsx": "32", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\AdminRedirect.jsx": "33", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx": "34", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx": "35", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\product.jsx": "36", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx": "37", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx": "38", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\rating.jsx": "39", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx": "40", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx": "41", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx": "42", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx": "43", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx": "44", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx": "45", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx": "46", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx": "47", "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx": "48"}, {"size": 670, "mtime": 1751023118411, "results": "49", "hashOfConfig": "50"}, {"size": 375, "mtime": 1751023118451, "results": "51", "hashOfConfig": "50"}, {"size": 5220, "mtime": 1751023118370, "results": "52", "hashOfConfig": "50"}, {"size": 4266, "mtime": 1751023118410, "results": "53", "hashOfConfig": "50"}, {"size": 4611, "mtime": 1751023118411, "results": "54", "hashOfConfig": "50"}, {"size": 1648, "mtime": 1751023118410, "results": "55", "hashOfConfig": "50"}, {"size": 2056, "mtime": 1751023118436, "results": "56", "hashOfConfig": "50"}, {"size": 577, "mtime": 1751023118437, "results": "57", "hashOfConfig": "50"}, {"size": 4181, "mtime": 1751023118434, "results": "58", "hashOfConfig": "50"}, {"size": 5158, "mtime": 1751023118444, "results": "59", "hashOfConfig": "50"}, {"size": 3266, "mtime": 1751023118447, "results": "60", "hashOfConfig": "50"}, {"size": 1940, "mtime": 1751023118435, "results": "61", "hashOfConfig": "50"}, {"size": 6320, "mtime": 1751023118438, "results": "62", "hashOfConfig": "50"}, {"size": 4706, "mtime": 1751023118445, "results": "63", "hashOfConfig": "50"}, {"size": 4755, "mtime": 1751023118447, "results": "64", "hashOfConfig": "50"}, {"size": 1935, "mtime": 1751023118438, "results": "65", "hashOfConfig": "50"}, {"size": 584, "mtime": 1751023118385, "results": "66", "hashOfConfig": "50"}, {"size": 572, "mtime": 1751023118372, "results": "67", "hashOfConfig": "50"}, {"size": 3480, "mtime": 1751023118446, "results": "68", "hashOfConfig": "50"}, {"size": 3186, "mtime": 1751023118437, "results": "69", "hashOfConfig": "50"}, {"size": 2832, "mtime": 1751023118446, "results": "70", "hashOfConfig": "50"}, {"size": 1846, "mtime": 1751023118386, "results": "71", "hashOfConfig": "50"}, {"size": 11625, "mtime": 1751023118424, "results": "72", "hashOfConfig": "50"}, {"size": 7112, "mtime": 1751023118419, "results": "73", "hashOfConfig": "50"}, {"size": 7638, "mtime": 1751023118422, "results": "74", "hashOfConfig": "50"}, {"size": 8275, "mtime": 1751023118434, "results": "75", "hashOfConfig": "50"}, {"size": 6946, "mtime": 1751023118413, "results": "76", "hashOfConfig": "50"}, {"size": 10778, "mtime": 1751023118423, "results": "77", "hashOfConfig": "50"}, {"size": 9100, "mtime": 1751023118424, "results": "78", "hashOfConfig": "50"}, {"size": 390, "mtime": 1751023118452, "results": "79", "hashOfConfig": "50"}, {"size": 413, "mtime": 1751023118386, "results": "80", "hashOfConfig": "50"}, {"size": 239, "mtime": 1751023118386, "results": "81", "hashOfConfig": "50"}, {"size": 727, "mtime": 1751023118372, "results": "82", "hashOfConfig": "50"}, {"size": 2674, "mtime": 1751024913400, "results": "83", "hashOfConfig": "50"}, {"size": 387, "mtime": 1751023118385, "results": "84", "hashOfConfig": "50"}, {"size": 1145, "mtime": 1751023118387, "results": "85", "hashOfConfig": "50"}, {"size": 1574, "mtime": 1751023118381, "results": "86", "hashOfConfig": "50"}, {"size": 923, "mtime": 1751023118398, "results": "87", "hashOfConfig": "50"}, {"size": 663, "mtime": 1751023118399, "results": "88", "hashOfConfig": "50"}, {"size": 1024, "mtime": 1751023118381, "results": "89", "hashOfConfig": "50"}, {"size": 3771, "mtime": 1751023118399, "results": "90", "hashOfConfig": "50"}, {"size": 760, "mtime": 1751023118380, "results": "91", "hashOfConfig": "50"}, {"size": 2539, "mtime": 1751023118386, "results": "92", "hashOfConfig": "50"}, {"size": 1262, "mtime": 1751023118399, "results": "93", "hashOfConfig": "50"}, {"size": 465, "mtime": 1751023118375, "results": "94", "hashOfConfig": "50"}, {"size": 1895, "mtime": 1751023118387, "results": "95", "hashOfConfig": "50"}, {"size": 3420, "mtime": 1751023118375, "results": "96", "hashOfConfig": "50"}, {"size": 3227, "mtime": 1751023118373, "results": "97", "hashOfConfig": "50"}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1mniaxs", {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\index.js", [], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\reportWebVitals.js", [], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\App.js", ["242"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\context\\cartContext.js", ["243", "244", "245"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\context\\userContext.js", ["246", "247", "248", "249", "250", "251", "252", "253", "254"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\context\\productsContext.js", ["255"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx", ["256", "257"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx", ["258"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx", ["259", "260"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx", ["261", "262", "263"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx", ["264"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx", [], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx", ["265", "266", "267", "268"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx", ["269", "270"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx", ["271", "272", "273", "274", "275", "276", "277", "278", "279"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx", ["280"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\footer.jsx", ["281"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx", [], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx", ["282", "283"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx", ["284", "285"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx", ["286"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\header.jsx", [], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx", [], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx", [], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx", [], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx", ["287"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx", [], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx", [], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminReviews.jsx", [], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\services\\httpService.js", ["288", "289"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\loader.jsx", [], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\message.jsx", [], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\AdminRedirect.jsx", ["290"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx", [], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx", [], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\product.jsx", [], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx", [], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx", [], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\rating.jsx", [], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx", [], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx", ["291", "292"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx", [], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx", ["293"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx", ["294"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx", [], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx", ["295", "296", "297"], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx", [], [], "D:\\CHUONGTRINHHOC\\NAM3\\DOANTHUCTAP\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx", [], [], {"ruleId": "298", "severity": 1, "message": "299", "line": 41, "column": 3, "nodeType": "300", "endLine": 41, "endColumn": 12, "suggestions": "301"}, {"ruleId": "302", "severity": 1, "message": "303", "line": 62, "column": 18, "nodeType": "304", "messageId": "305", "endLine": 62, "endColumn": 20}, {"ruleId": "302", "severity": 1, "message": "303", "line": 68, "column": 15, "nodeType": "304", "messageId": "305", "endLine": 68, "endColumn": 17}, {"ruleId": "302", "severity": 1, "message": "303", "line": 125, "column": 45, "nodeType": "304", "messageId": "305", "endLine": 125, "endColumn": 47}, {"ruleId": "306", "severity": 1, "message": "307", "line": 58, "column": 15, "nodeType": "300", "messageId": "308", "endLine": 58, "endColumn": 19}, {"ruleId": "298", "severity": 1, "message": "309", "line": 104, "column": 6, "nodeType": "310", "endLine": 104, "endColumn": 8, "suggestions": "311"}, {"ruleId": "298", "severity": 1, "message": "312", "line": 116, "column": 6, "nodeType": "310", "endLine": 116, "endColumn": 18, "suggestions": "313"}, {"ruleId": "302", "severity": 1, "message": "314", "line": 121, "column": 20, "nodeType": "304", "messageId": "305", "endLine": 121, "endColumn": 22}, {"ruleId": "302", "severity": 1, "message": "314", "line": 121, "column": 53, "nodeType": "304", "messageId": "305", "endLine": 121, "endColumn": 55}, {"ruleId": "302", "severity": 1, "message": "314", "line": 125, "column": 17, "nodeType": "304", "messageId": "305", "endLine": 125, "endColumn": 19}, {"ruleId": "302", "severity": 1, "message": "314", "line": 125, "column": 44, "nodeType": "304", "messageId": "305", "endLine": 125, "endColumn": 46}, {"ruleId": "302", "severity": 1, "message": "314", "line": 129, "column": 20, "nodeType": "304", "messageId": "305", "endLine": 129, "endColumn": 22}, {"ruleId": "302", "severity": 1, "message": "303", "line": 133, "column": 16, "nodeType": "304", "messageId": "305", "endLine": 133, "endColumn": 18}, {"ruleId": "302", "severity": 1, "message": "303", "line": 35, "column": 49, "nodeType": "304", "messageId": "305", "endLine": 35, "endColumn": 51}, {"ruleId": "298", "severity": 1, "message": "315", "line": 22, "column": 6, "nodeType": "310", "endLine": 22, "endColumn": 8, "suggestions": "316"}, {"ruleId": "302", "severity": 1, "message": "314", "line": 26, "column": 13, "nodeType": "304", "messageId": "305", "endLine": 26, "endColumn": 15}, {"ruleId": "298", "severity": 1, "message": "317", "line": 15, "column": 6, "nodeType": "310", "endLine": 15, "endColumn": 8, "suggestions": "318"}, {"ruleId": "306", "severity": 1, "message": "319", "line": 1, "column": 29, "nodeType": "300", "messageId": "308", "endLine": 1, "endColumn": 38}, {"ruleId": "302", "severity": 1, "message": "314", "line": 25, "column": 13, "nodeType": "304", "messageId": "305", "endLine": 25, "endColumn": 15}, {"ruleId": "306", "severity": 1, "message": "320", "line": 29, "column": 11, "nodeType": "300", "messageId": "308", "endLine": 29, "endColumn": 13}, {"ruleId": "302", "severity": 1, "message": "303", "line": 59, "column": 38, "nodeType": "304", "messageId": "305", "endLine": 59, "endColumn": 40}, {"ruleId": "302", "severity": 1, "message": "303", "line": 129, "column": 53, "nodeType": "304", "messageId": "305", "endLine": 129, "endColumn": 55}, {"ruleId": "298", "severity": 1, "message": "321", "line": 31, "column": 6, "nodeType": "310", "endLine": 31, "endColumn": 8, "suggestions": "322"}, {"ruleId": "302", "severity": 1, "message": "303", "line": 26, "column": 47, "nodeType": "304", "messageId": "305", "endLine": 26, "endColumn": 49}, {"ruleId": "302", "severity": 1, "message": "303", "line": 27, "column": 47, "nodeType": "304", "messageId": "305", "endLine": 27, "endColumn": 49}, {"ruleId": "298", "severity": 1, "message": "323", "line": 34, "column": 6, "nodeType": "310", "endLine": 34, "endColumn": 8, "suggestions": "324"}, {"ruleId": "302", "severity": 1, "message": "314", "line": 40, "column": 17, "nodeType": "304", "messageId": "305", "endLine": 40, "endColumn": 19}, {"ruleId": "298", "severity": 1, "message": "325", "line": 34, "column": 6, "nodeType": "310", "endLine": 34, "endColumn": 8, "suggestions": "326"}, {"ruleId": "302", "severity": 1, "message": "314", "line": 43, "column": 13, "nodeType": "304", "messageId": "305", "endLine": 43, "endColumn": 15}, {"ruleId": "306", "severity": 1, "message": "327", "line": 18, "column": 24, "nodeType": "300", "messageId": "308", "endLine": 18, "endColumn": 39}, {"ruleId": "298", "severity": 1, "message": "328", "line": 43, "column": 6, "nodeType": "310", "endLine": 43, "endColumn": 8, "suggestions": "329"}, {"ruleId": "302", "severity": 1, "message": "314", "line": 49, "column": 21, "nodeType": "304", "messageId": "305", "endLine": 49, "endColumn": 23}, {"ruleId": "302", "severity": 1, "message": "303", "line": 51, "column": 34, "nodeType": "304", "messageId": "305", "endLine": 51, "endColumn": 36}, {"ruleId": "302", "severity": 1, "message": "314", "line": 55, "column": 24, "nodeType": "304", "messageId": "305", "endLine": 55, "endColumn": 26}, {"ruleId": "302", "severity": 1, "message": "303", "line": 57, "column": 37, "nodeType": "304", "messageId": "305", "endLine": 57, "endColumn": 39}, {"ruleId": "302", "severity": 1, "message": "314", "line": 63, "column": 13, "nodeType": "304", "messageId": "305", "endLine": 63, "endColumn": 15}, {"ruleId": "302", "severity": 1, "message": "314", "line": 63, "column": 35, "nodeType": "304", "messageId": "305", "endLine": 63, "endColumn": 37}, {"ruleId": "302", "severity": 1, "message": "314", "line": 66, "column": 20, "nodeType": "304", "messageId": "305", "endLine": 66, "endColumn": 22}, {"ruleId": "302", "severity": 1, "message": "303", "line": 37, "column": 33, "nodeType": "304", "messageId": "305", "endLine": 37, "endColumn": 35}, {"ruleId": "330", "severity": 1, "message": "331", "line": 14, "column": 13, "nodeType": "332", "messageId": "333", "endLine": 14, "endColumn": 107, "fix": "334"}, {"ruleId": "335", "severity": 1, "message": "336", "line": 18, "column": 28, "nodeType": "300", "messageId": "337", "endLine": 18, "endColumn": 36}, {"ruleId": "298", "severity": 1, "message": "338", "line": 22, "column": 6, "nodeType": "310", "endLine": 22, "endColumn": 8, "suggestions": "339"}, {"ruleId": "306", "severity": 1, "message": "327", "line": 13, "column": 23, "nodeType": "300", "messageId": "308", "endLine": 13, "endColumn": 38}, {"ruleId": "298", "severity": 1, "message": "338", "line": 27, "column": 6, "nodeType": "310", "endLine": 27, "endColumn": 8, "suggestions": "340"}, {"ruleId": "298", "severity": 1, "message": "321", "line": 22, "column": 6, "nodeType": "310", "endLine": 22, "endColumn": 8, "suggestions": "341"}, {"ruleId": "306", "severity": 1, "message": "342", "line": 2, "column": 55, "nodeType": "300", "messageId": "308", "endLine": 2, "endColumn": 59}, {"ruleId": "302", "severity": 1, "message": "303", "line": 4, "column": 13, "nodeType": "304", "messageId": "305", "endLine": 4, "endColumn": 15}, {"ruleId": "343", "severity": 1, "message": "344", "line": 11, "column": 1, "nodeType": "345", "endLine": 18, "endColumn": 3}, {"ruleId": "306", "severity": 1, "message": "346", "line": 1, "column": 8, "nodeType": "300", "messageId": "308", "endLine": 1, "endColumn": 13}, {"ruleId": "302", "severity": 1, "message": "303", "line": 90, "column": 34, "nodeType": "304", "messageId": "305", "endLine": 90, "endColumn": 36}, {"ruleId": "302", "severity": 1, "message": "303", "line": 90, "column": 51, "nodeType": "304", "messageId": "305", "endLine": 90, "endColumn": 53}, {"ruleId": "302", "severity": 1, "message": "303", "line": 21, "column": 47, "nodeType": "304", "messageId": "305", "endLine": 21, "endColumn": 49}, {"ruleId": "306", "severity": 1, "message": "319", "line": 1, "column": 17, "nodeType": "300", "messageId": "308", "endLine": 1, "endColumn": 26}, {"ruleId": "306", "severity": 1, "message": "319", "line": 1, "column": 17, "nodeType": "300", "messageId": "308", "endLine": 1, "endColumn": 26}, {"ruleId": "306", "severity": 1, "message": "347", "line": 8, "column": 8, "nodeType": "300", "messageId": "308", "endLine": 8, "endColumn": 19}, {"ruleId": "306", "severity": 1, "message": "348", "line": 14, "column": 10, "nodeType": "300", "messageId": "308", "endLine": 14, "endColumn": 15}, "react-hooks/exhaustive-deps", "React Hook useEffect contains a call to 'setKeyword'. Without a list of dependencies, this can lead to an infinite chain of updates. To fix this, pass [keywordParam] as a second argument to the useEffect Hook.", "Identifier", ["349"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "no-unused-vars", "'data' is assigned a value but never used.", "unusedVar", "React Hook useEffect has missing dependencies: 'authTokens' and 'refresh'. Either include them or remove the dependency array.", "ArrayExpression", ["350"], "React Hook useEffect has a missing dependency: 'refresh'. Either include it or remove the dependency array.", ["351"], "Expected '!==' and instead saw '!='.", "React Hook useEffect has a missing dependency: 'loadProducts'. Either include it or remove the dependency array.", ["352"], "React Hook useEffect has missing dependencies: 'logout', 'navigate', and 'userInfo'. Either include them or remove the dependency array.", ["353"], "'useEffect' is defined but never used.", "'id' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'navigate' and 'userInfo'. Either include them or remove the dependency array.", ["354"], "React Hook useEffect has missing dependencies: 'id' and 'logout'. Either include them or remove the dependency array.", ["355"], "React Hook useEffect has missing dependencies: 'id' and 'loadProduct'. Either include them or remove the dependency array.", ["356"], "'setSearchParams' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'brandParam', 'categoryParam', and 'loadProducts'. Either include them or remove the dependency array.", ["357"], "react/jsx-no-target-blank", "Using target=\"_blank\" without rel=\"noreferrer\" (which implies rel=\"noopener\") is a security risk in older browsers: see https://mathiasbynens.github.io/rel-noopener/#recommendations", "JSXOpeningElement", "noTargetBlankWithoutNoreferrer", {"range": "358", "text": "359"}, "no-const-assign", "'redirect' is constant.", "const", "React Hook useEffect has missing dependencies: 'navigate', 'redirect', and 'userInfo'. Either include them or remove the dependency array.", ["360"], ["361"], ["362"], "'Form' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'React' is defined but never used.", "'httpService' is defined but never used.", "'email' is assigned a value but never used.", {"desc": "363", "fix": "364"}, {"desc": "365", "fix": "366"}, {"desc": "365", "fix": "367"}, {"desc": "368", "fix": "369"}, {"desc": "370", "fix": "371"}, {"desc": "372", "fix": "373"}, {"desc": "374", "fix": "375"}, {"desc": "376", "fix": "377"}, {"desc": "378", "fix": "379"}, [460, 460], " rel=\"noreferrer\"", {"desc": "380", "fix": "381"}, {"desc": "380", "fix": "382"}, {"desc": "372", "fix": "383"}, "Add dependencies array: [keywordParam]", {"range": "384", "text": "385"}, "Update the dependencies array to be: [authTokens, refresh]", {"range": "386", "text": "387"}, {"range": "388", "text": "387"}, "Update the dependencies array to be: [loadProducts]", {"range": "389", "text": "390"}, "Update the dependencies array to be: [logout, navigate, userInfo]", {"range": "391", "text": "392"}, "Update the dependencies array to be: [navigate, userInfo]", {"range": "393", "text": "394"}, "Update the dependencies array to be: [id, logout]", {"range": "395", "text": "396"}, "Update the dependencies array to be: [id, loadProduct]", {"range": "397", "text": "398"}, "Update the dependencies array to be: [brandParam, categoryParam, loadProducts]", {"range": "399", "text": "400"}, "Update the dependencies array to be: [navigate, redirect, userInfo]", {"range": "401", "text": "402"}, {"range": "403", "text": "402"}, {"range": "404", "text": "394"}, [1876, 1876], ", [keywordParam]", [2899, 2901], "[authTokens, refresh]", [3287, 3299], [860, 862], "[loadProducts]", [505, 507], "[logout, navigate, userInfo]", [1198, 1200], "[navigate, userInfo]", [1319, 1321], "[id, logout]", [1044, 1046], "[id, loadProduct]", [1530, 1532], "[brandParam, categoryParam, loadProducts]", [854, 856], "[navigate, redirect, userInfo]", [982, 984], [879, 881]]
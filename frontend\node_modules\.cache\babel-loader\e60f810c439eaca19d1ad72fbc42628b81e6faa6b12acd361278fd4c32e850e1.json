{"ast": null, "code": "var _jsxFileName = \"D:\\\\CHUONGTRINHHOC\\\\NAM3\\\\DOANTHUCTAP\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\stripePaymentWrapper.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Elements } from '@stripe/react-stripe-js';\nimport { loadStripe } from '@stripe/stripe-js';\nimport PaymentForm from './paymentForm';\nimport httpService from '../services/httpService';\nimport Message from './message';\n\n// Cập nhật publishable key - phải khớp với secret key trong backend\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst stripePromise = loadStripe(\"pk_test_51ReEgVQ2LebguEiky8SYJgQlfD0xKqGZ3EV6RbwHcauQlFDsvd6zo7sj5JiyIUpSz7zY62nrfLkRju8dxoOlEs0P00MfHxwsiJ\");\nexport default function StripePaymentWrapper(_ref) {\n  _s();\n  let {\n    id\n  } = _ref;\n  const [clientSecret, setClientSecret] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const requestSentRef = useRef(false);\n  useEffect(() => {\n    const createPaymentIntent = async () => {\n      // Nếu đã gửi request hoặc đã có clientSecret, không gửi lại\n      if (requestSentRef.current || clientSecret) {\n        return;\n      }\n\n      // Đánh dấu đã gửi request\n      requestSentRef.current = true;\n      try {\n        setLoading(true);\n        console.log(\"Creating payment intent for order:\", id);\n        const {\n          data\n        } = await httpService.post(\"/api/stripe-payment/\", {\n          order: id\n        });\n        if (data.clientSecret) {\n          console.log(\"Received client secret successfully\");\n          setClientSecret(data.clientSecret);\n        } else {\n          console.error(\"No client secret in response:\", data);\n          setError(\"Failed to initialize payment form\");\n        }\n      } catch (ex) {\n        var _ex$response, _ex$response2, _ex$response3, _ex$response4, _ex$response5, _ex$response5$data;\n        console.error(\"Error creating payment intent:\", ex);\n        console.error(\"Response data:\", (_ex$response = ex.response) === null || _ex$response === void 0 ? void 0 : _ex$response.data);\n        console.error(\"Response status:\", (_ex$response2 = ex.response) === null || _ex$response2 === void 0 ? void 0 : _ex$response2.status);\n        let errorMessage = \"Could not initialize payment: \";\n        if (((_ex$response3 = ex.response) === null || _ex$response3 === void 0 ? void 0 : _ex$response3.status) === 404) {\n          errorMessage += \"Backend server not running or API endpoint not found\";\n        } else if (((_ex$response4 = ex.response) === null || _ex$response4 === void 0 ? void 0 : _ex$response4.status) === 500) {\n          errorMessage += \"Server error - check Stripe configuration\";\n        } else if ((_ex$response5 = ex.response) !== null && _ex$response5 !== void 0 && (_ex$response5$data = _ex$response5.data) !== null && _ex$response5$data !== void 0 && _ex$response5$data.error) {\n          errorMessage += ex.response.data.error;\n        } else if (ex.message) {\n          errorMessage += ex.message;\n        } else {\n          errorMessage += \"Unknown error occurred\";\n        }\n        setError(errorMessage);\n        // Reset flag nếu có lỗi để có thể thử lại\n        requestSentRef.current = false;\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (id) {\n      createPaymentIntent();\n    }\n  }, [id, clientSecret]);\n  const appearance = {\n    theme: \"stripe\",\n    variables: {\n      colorPrimary: '#0570de'\n    }\n  };\n  const options = clientSecret ? {\n    clientSecret,\n    appearance\n  } : {};\n  const retryPayment = () => {\n    setError('');\n    setClientSecret('');\n    requestSentRef.current = false;\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"Loading payment form...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Message, {\n      variant: \"danger\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: retryPayment,\n      className: \"btn btn-primary mt-2\",\n      children: \"Try Again\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 5\n  }, this);\n  if (!clientSecret) return /*#__PURE__*/_jsxDEV(Message, {\n    variant: \"danger\",\n    children: \"Could not initialize payment form\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 100,\n    columnNumber: 29\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"payment\",\n    children: /*#__PURE__*/_jsxDEV(Elements, {\n      options: options,\n      stripe: stripePromise,\n      children: /*#__PURE__*/_jsxDEV(PaymentForm, {\n        id: id\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 5\n  }, this);\n}\n_s(StripePaymentWrapper, \"+qIEXbJhR1nGijzmfXYTNf77slQ=\");\n_c = StripePaymentWrapper;\nvar _c;\n$RefreshReg$(_c, \"StripePaymentWrapper\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Elements", "loadStripe", "PaymentForm", "httpService", "Message", "jsxDEV", "_jsxDEV", "stripePromise", "StripePaymentWrapper", "_ref", "_s", "id", "clientSecret", "setClientSecret", "loading", "setLoading", "error", "setError", "requestSentRef", "createPaymentIntent", "current", "console", "log", "data", "post", "order", "ex", "_ex$response", "_ex$response2", "_ex$response3", "_ex$response4", "_ex$response5", "_ex$response5$data", "response", "status", "errorMessage", "message", "appearance", "theme", "variables", "colorPrimary", "options", "retryPayment", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "className", "stripe", "_c", "$RefreshReg$"], "sources": ["D:/CHUONGTRINHHOC/NAM3/DOANTHUCTAP/Python-KienTap-/frontend/src/components/stripePaymentWrapper.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\nimport { Elements } from '@stripe/react-stripe-js';\r\nimport { loadStripe } from '@stripe/stripe-js';\r\nimport PaymentForm from './paymentForm';\r\nimport httpService from '../services/httpService';\r\nimport Message from './message';\r\n\r\n// Cập nhật publishable key - phải khớp với secret key trong backend\r\nconst stripePromise = loadStripe(\r\n  \"pk_test_51ReEgVQ2LebguEiky8SYJgQlfD0xKqGZ3EV6RbwHcauQlFDsvd6zo7sj5JiyIUpSz7zY62nrfLkRju8dxoOlEs0P00MfHxwsiJ\"\r\n);\r\n\r\nexport default function StripePaymentWrapper({ id }) {\r\n  const [clientSecret, setClientSecret] = useState('');\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const requestSentRef = useRef(false);\r\n\r\n  useEffect(() => {\r\n    const createPaymentIntent = async () => {\r\n      // Nếu đã gửi request hoặc đã có clientSecret, không gửi lại\r\n      if (requestSentRef.current || clientSecret) {\r\n        return;\r\n      }\r\n      \r\n      // Đánh dấu đã gửi request\r\n      requestSentRef.current = true;\r\n      \r\n      try {\r\n        setLoading(true);\r\n        console.log(\"Creating payment intent for order:\", id);\r\n        \r\n        const { data } = await httpService.post(\"/api/stripe-payment/\", {order: id});\r\n        \r\n        if (data.clientSecret) {\r\n          console.log(\"Received client secret successfully\");\r\n          setClientSecret(data.clientSecret);\r\n        } else {\r\n          console.error(\"No client secret in response:\", data);\r\n          setError(\"Failed to initialize payment form\");\r\n        }\r\n      } catch (ex) {\r\n        console.error(\"Error creating payment intent:\", ex);\r\n        console.error(\"Response data:\", ex.response?.data);\r\n        console.error(\"Response status:\", ex.response?.status);\r\n\r\n        let errorMessage = \"Could not initialize payment: \";\r\n        if (ex.response?.status === 404) {\r\n          errorMessage += \"Backend server not running or API endpoint not found\";\r\n        } else if (ex.response?.status === 500) {\r\n          errorMessage += \"Server error - check Stripe configuration\";\r\n        } else if (ex.response?.data?.error) {\r\n          errorMessage += ex.response.data.error;\r\n        } else if (ex.message) {\r\n          errorMessage += ex.message;\r\n        } else {\r\n          errorMessage += \"Unknown error occurred\";\r\n        }\r\n\r\n        setError(errorMessage);\r\n        // Reset flag nếu có lỗi để có thể thử lại\r\n        requestSentRef.current = false;\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    \r\n    if (id) {\r\n      createPaymentIntent();\r\n    }\r\n  }, [id, clientSecret]);\r\n\r\n  const appearance = {\r\n    theme: \"stripe\",\r\n    variables: {\r\n      colorPrimary: '#0570de',\r\n    }\r\n  };\r\n\r\n  const options = clientSecret ? {\r\n    clientSecret,\r\n    appearance,\r\n  } : {};\r\n\r\n  const retryPayment = () => {\r\n    setError('');\r\n    setClientSecret('');\r\n    requestSentRef.current = false;\r\n  };\r\n\r\n  if (loading) return <div>Loading payment form...</div>;\r\n  if (error) return (\r\n    <div>\r\n      <Message variant=\"danger\">{error}</Message>\r\n      <button onClick={retryPayment} className=\"btn btn-primary mt-2\">\r\n        Try Again\r\n      </button>\r\n    </div>\r\n  );\r\n  if (!clientSecret) return <Message variant=\"danger\">Could not initialize payment form</Message>;\r\n\r\n  return (\r\n    <div className=\"payment\">\r\n      <Elements options={options} stripe={stripePromise}>\r\n        <PaymentForm id={id} />\r\n      </Elements>\r\n    </div>\r\n  );\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,OAAO,MAAM,WAAW;;AAE/B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,aAAa,GAAGN,UAAU,CAC9B,6GAA6G,CAC9G;AAED,eAAe,SAASO,oBAAoBA,CAAAC,IAAA,EAAS;EAAAC,EAAA;EAAA,IAAR;IAAEC;EAAG,CAAC,GAAAF,IAAA;EACjD,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMqB,cAAc,GAAGnB,MAAM,CAAC,KAAK,CAAC;EAEpCD,SAAS,CAAC,MAAM;IACd,MAAMqB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;MACtC;MACA,IAAID,cAAc,CAACE,OAAO,IAAIR,YAAY,EAAE;QAC1C;MACF;;MAEA;MACAM,cAAc,CAACE,OAAO,GAAG,IAAI;MAE7B,IAAI;QACFL,UAAU,CAAC,IAAI,CAAC;QAChBM,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEX,EAAE,CAAC;QAErD,MAAM;UAAEY;QAAK,CAAC,GAAG,MAAMpB,WAAW,CAACqB,IAAI,CAAC,sBAAsB,EAAE;UAACC,KAAK,EAAEd;QAAE,CAAC,CAAC;QAE5E,IAAIY,IAAI,CAACX,YAAY,EAAE;UACrBS,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;UAClDT,eAAe,CAACU,IAAI,CAACX,YAAY,CAAC;QACpC,CAAC,MAAM;UACLS,OAAO,CAACL,KAAK,CAAC,+BAA+B,EAAEO,IAAI,CAAC;UACpDN,QAAQ,CAAC,mCAAmC,CAAC;QAC/C;MACF,CAAC,CAAC,OAAOS,EAAE,EAAE;QAAA,IAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,kBAAA;QACXX,OAAO,CAACL,KAAK,CAAC,gCAAgC,EAAEU,EAAE,CAAC;QACnDL,OAAO,CAACL,KAAK,CAAC,gBAAgB,GAAAW,YAAA,GAAED,EAAE,CAACO,QAAQ,cAAAN,YAAA,uBAAXA,YAAA,CAAaJ,IAAI,CAAC;QAClDF,OAAO,CAACL,KAAK,CAAC,kBAAkB,GAAAY,aAAA,GAAEF,EAAE,CAACO,QAAQ,cAAAL,aAAA,uBAAXA,aAAA,CAAaM,MAAM,CAAC;QAEtD,IAAIC,YAAY,GAAG,gCAAgC;QACnD,IAAI,EAAAN,aAAA,GAAAH,EAAE,CAACO,QAAQ,cAAAJ,aAAA,uBAAXA,aAAA,CAAaK,MAAM,MAAK,GAAG,EAAE;UAC/BC,YAAY,IAAI,sDAAsD;QACxE,CAAC,MAAM,IAAI,EAAAL,aAAA,GAAAJ,EAAE,CAACO,QAAQ,cAAAH,aAAA,uBAAXA,aAAA,CAAaI,MAAM,MAAK,GAAG,EAAE;UACtCC,YAAY,IAAI,2CAA2C;QAC7D,CAAC,MAAM,KAAAJ,aAAA,GAAIL,EAAE,CAACO,QAAQ,cAAAF,aAAA,gBAAAC,kBAAA,GAAXD,aAAA,CAAaR,IAAI,cAAAS,kBAAA,eAAjBA,kBAAA,CAAmBhB,KAAK,EAAE;UACnCmB,YAAY,IAAIT,EAAE,CAACO,QAAQ,CAACV,IAAI,CAACP,KAAK;QACxC,CAAC,MAAM,IAAIU,EAAE,CAACU,OAAO,EAAE;UACrBD,YAAY,IAAIT,EAAE,CAACU,OAAO;QAC5B,CAAC,MAAM;UACLD,YAAY,IAAI,wBAAwB;QAC1C;QAEAlB,QAAQ,CAACkB,YAAY,CAAC;QACtB;QACAjB,cAAc,CAACE,OAAO,GAAG,KAAK;MAChC,CAAC,SAAS;QACRL,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIJ,EAAE,EAAE;MACNQ,mBAAmB,EAAE;IACvB;EACF,CAAC,EAAE,CAACR,EAAE,EAAEC,YAAY,CAAC,CAAC;EAEtB,MAAMyB,UAAU,GAAG;IACjBC,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE;MACTC,YAAY,EAAE;IAChB;EACF,CAAC;EAED,MAAMC,OAAO,GAAG7B,YAAY,GAAG;IAC7BA,YAAY;IACZyB;EACF,CAAC,GAAG,CAAC,CAAC;EAEN,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzBzB,QAAQ,CAAC,EAAE,CAAC;IACZJ,eAAe,CAAC,EAAE,CAAC;IACnBK,cAAc,CAACE,OAAO,GAAG,KAAK;EAChC,CAAC;EAED,IAAIN,OAAO,EAAE,oBAAOR,OAAA;IAAAqC,QAAA,EAAK;EAAuB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QAAM;EACtD,IAAI/B,KAAK,EAAE,oBACTV,OAAA;IAAAqC,QAAA,gBACErC,OAAA,CAACF,OAAO;MAAC4C,OAAO,EAAC,QAAQ;MAAAL,QAAA,EAAE3B;IAAK;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAW,eAC3CzC,OAAA;MAAQ2C,OAAO,EAAEP,YAAa;MAACQ,SAAS,EAAC,sBAAsB;MAAAP,QAAA,EAAC;IAEhE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAS;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACL;EAER,IAAI,CAACnC,YAAY,EAAE,oBAAON,OAAA,CAACF,OAAO;IAAC4C,OAAO,EAAC,QAAQ;IAAAL,QAAA,EAAC;EAAiC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QAAU;EAE/F,oBACEzC,OAAA;IAAK4C,SAAS,EAAC,SAAS;IAAAP,QAAA,eACtBrC,OAAA,CAACN,QAAQ;MAACyC,OAAO,EAAEA,OAAQ;MAACU,MAAM,EAAE5C,aAAc;MAAAoC,QAAA,eAChDrC,OAAA,CAACJ,WAAW;QAACS,EAAE,EAAEA;MAAG;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAG;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EACd;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACP;AAEV;AAACrC,EAAA,CAhGuBF,oBAAoB;AAAA4C,EAAA,GAApB5C,oBAAoB;AAAA,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}